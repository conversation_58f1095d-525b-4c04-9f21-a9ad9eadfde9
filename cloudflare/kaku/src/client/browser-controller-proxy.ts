declare global {
  interface Window {
    browserController: any;
  }
}

(function () {
  const config = {
    debug: true,
  };

  let messageChannel: MessageChannel | null = null;
  let controlTabPort: MessagePort | null = null;
  let isInitialized = false;
  let messageId = 0;
  let pendingMessages = new Map<number, { resolve: Function; reject: Function }>();

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[browserControllerProxy]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[browserControllerProxy]', ...args);
  }

  /**
   * Initializes the proxy by establishing communication with the control tab
   * @param browserFullWsEndpoint - WebSocket endpoint (not used in proxy, passed for compatibility)
   * @param targetId - Target ID (not used in proxy, passed for compatibility)
   */
  async function init(browserFullWsEndpoint: string, targetId: string): Promise<void> {
    if (isInitialized) {
      log('Proxy already initialized');
      return;
    }

    log('Initializing browser controller proxy...');

    // Wait for the control tab to establish communication
    await waitForControlTabConnection();

    log('Browser controller proxy initialized');
    isInitialized = true;
  }

  /**
   * Waits for the control tab to establish communication via MessageChannel
   * Now checks for a globally stored port from the control tab
   */
  async function waitForControlTabConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout waiting for control tab connection'));
      }, 10000); // 10 second timeout

      // Check for the global port with polling
      const checkForGlobalPort = () => {
        const globalPort = (globalThis as any).__kakuMessageChannelPort;
        const globalTargetId = (globalThis as any).__kakuTargetId;

        if (globalPort && globalTargetId) {
          clearTimeout(timeout);
          clearInterval(pollInterval);

          log('Found MessageChannel port from control tab');

          // Get the port from global storage
          controlTabPort = globalPort;

          // Set up message handling
          controlTabPort.onmessage = handleControlTabResponse;

          // Start the port
          controlTabPort.start();

          // Clean up global storage
          delete (globalThis as any).__kakuMessageChannelPort;
          delete (globalThis as any).__kakuTargetId;

          log('MessageChannel established with control tab');
          resolve();
        }
      };

      // Check immediately
      checkForGlobalPort();

      // Poll every 100ms for the global port
      const pollInterval = setInterval(checkForGlobalPort, 100);

      // Also keep the original message listener as fallback
      (globalThis as any).addEventListener('message', function handleControlTabMessage(event: any) {
        if (event.data?.type === 'CONTROL_TAB_CHANNEL') {
          clearTimeout(timeout);
          clearInterval(pollInterval);
          (globalThis as any).removeEventListener('message', handleControlTabMessage);

          log('Received MessageChannel from control tab via postMessage');

          // Get the port from the control tab
          controlTabPort = event.ports[0];

          // Set up message handling
          controlTabPort.onmessage = handleControlTabResponse;

          // Start the port
          controlTabPort.start();

          log('MessageChannel established with control tab');
          resolve();
        }
      });
    });
  }

  /**
   * Handles responses from the control tab
   */
  function handleControlTabResponse(event: MessageEvent) {
    const { id, response, error: responseError } = event.data as any;

    if (pendingMessages.has(id)) {
      const { resolve, reject } = pendingMessages.get(id)!;
      pendingMessages.delete(id);

      if (responseError) {
        reject(new Error(responseError));
      } else {
        resolve(response);
      }
    }
  }

  /**
   * Sends a message to the control tab and returns a promise that resolves with the response
   */
  async function sendToControlTab(method: string, params: any[] = []): Promise<any> {
    if (!controlTabPort) {
      throw new Error('Control tab connection not established');
    }

    const id = ++messageId;

    return new Promise((resolve, reject) => {
      // Store the promise resolvers
      pendingMessages.set(id, { resolve, reject });

      // Set up timeout
      const timeout = setTimeout(() => {
        if (pendingMessages.has(id)) {
          pendingMessages.delete(id);
          reject(new Error(`Timeout waiting for response to ${method}`));
        }
      }, 30000); // 30 second timeout for CDP operations

      // Send the message
      controlTabPort!.postMessage({
        id,
        method,
        params,
      });

      // Clear timeout when promise resolves/rejects
      const originalResolve = resolve;
      const originalReject = reject;

      pendingMessages.set(id, {
        resolve: (value: any) => {
          clearTimeout(timeout);
          originalResolve(value);
        },
        reject: (reason: any) => {
          clearTimeout(timeout);
          originalReject(reason);
        },
      });
    });
  }

  /**
   * Sets up browser window bounds and device metrics
   */
  async function setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    return sendToControlTab('setupBrowserMetrics', [viewPort]);
  }

  /**
   * Dispatches a mouse move event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    return sendToControlTab('dispatchMouseMove', [x, y]);
  }

  /**
   * Dispatches a mouse down event
   */
  async function dispatchMouseDown(x: number, y: number, button: string = 'left'): Promise<void> {
    return sendToControlTab('dispatchMouseDown', [x, y, button]);
  }

  /**
   * Dispatches a mouse up event
   */
  async function dispatchMouseUp(x: number, y: number, button: string = 'left'): Promise<void> {
    return sendToControlTab('dispatchMouseUp', [x, y, button]);
  }

  /**
   * Dispatches a mouse click event
   */
  async function dispatchMouseClick(x: number, y: number, button: string = 'left'): Promise<void> {
    return sendToControlTab('dispatchMouseClick', [x, y, button]);
  }

  /**
   * Dispatches a key event
   */
  async function dispatchKeyEvent(type: string, keyData: any): Promise<void> {
    return sendToControlTab('dispatchKeyEvent', [type, keyData]);
  }

  /**
   * Inserts text at the current cursor position
   */
  async function insertText(text: string): Promise<void> {
    return sendToControlTab('insertText', [text]);
  }

  /**
   * Takes a screenshot using CDP
   */
  async function takeScreenshot(): Promise<string | null> {
    return sendToControlTab('takeScreenshot', []);
  }

  /**
   * Handles input events
   */
  async function handleInputEvent(eventData: any): Promise<any> {
    return sendToControlTab('handleInputEvent', [eventData]);
  }

  /**
   * Requests a new frame
   */
  async function requestNewFrame(): Promise<void> {
    return sendToControlTab('requestNewFrame', []);
  }

  /**
   * Triggers mouse movement to generate frames
   */
  async function triggerMouseMovement(): Promise<void> {
    return sendToControlTab('triggerMouseMovement', []);
  }

  // Expose public API - identical to the original browser-controller
  (globalThis as any).browserController = {
    init,
    setupBrowserMetrics,
    dispatchMouseMove,
    dispatchMouseDown,
    dispatchMouseUp,
    dispatchMouseClick,
    dispatchKeyEvent,
    insertText,
    takeScreenshot,
    handleInputEvent,
    requestNewFrame,
    triggerMouseMovement,
  };
})();
