import Protocol from 'devtools-protocol';
import { CDP } from '../browser/simple-cdp';

declare global {
  interface Window {
    browserController: any;
  }
}

(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;
  let targetTabPort: any = null; // MessagePort for communication with target tab
  let isControlTab = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[browserController]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[browserController]', ...args);
  }

  /**
   * Initializes the browser controller with its own CDP session
   * Similar to puppeteer.connect(wsEndpoint) - creates own session context
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to (passed from connections-workflow)
   * @param targetTabWindow - Optional window reference for target tab (control tab mode)
   */
  async function init(
    browserFullWsEndpoint: string,
    targetId: string,
    targetTabWindow?: any,
  ): Promise<void> {
    log('Connecting to CDP and attaching to target:', targetId);
    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);
    log('Browser controller initialized with sessionId:', sessionId);

    // If targetTabWindow is provided, this is the control tab
    if (targetTabWindow) {
      isControlTab = true;
      await setupControlTabCommunication(targetTabWindow);
    }
  }

  /**
   * Sets up communication with the target tab when running in control tab mode
   * @param targetTabWindow - Object containing targetId for the target tab
   */
  async function setupControlTabCommunication(targetTabWindow: any): Promise<void> {
    log('Setting up control tab communication...');

    // Create a MessageChannel for communication
    const messageChannel = new MessageChannel();
    targetTabPort = messageChannel.port1;

    // Set up message handling
    targetTabPort.onmessage = handleTargetTabMessage;
    targetTabPort.start();

    // Get the actual window reference using CDP and send the MessageChannel port
    await sendMessageChannelToTargetTab(targetTabWindow.targetId, messageChannel.port2);

    log('Control tab communication established');
  }

  /**
   * Sends the MessageChannel port to the target tab using a simpler approach
   * Since both tabs are in the same browser, we can use a shared global approach
   * @param targetId - The target ID of the target tab
   * @param port - The MessagePort to send to the target tab
   */
  async function sendMessageChannelToTargetTab(targetId: string, port: any): Promise<void> {
    log('Setting up MessageChannel communication with target tab...');

    try {
      // Store the port in a way that the target tab can access it
      // We'll use the browser's global storage mechanism
      (globalThis as any).__kakuMessageChannelPort = port;
      (globalThis as any).__kakuTargetId = targetId;

      // Use a simple polling mechanism to check if the target tab is ready
      // The target tab (browser-controller-proxy) will check for this global
      log('MessageChannel port stored globally for target tab access');

      // Set up a cleanup timeout
      setTimeout(() => {
        if ((globalThis as any).__kakuMessageChannelPort) {
          log('Cleaning up unused MessageChannel port');
          delete (globalThis as any).__kakuMessageChannelPort;
          delete (globalThis as any).__kakuTargetId;
        }
      }, 30000); // 30 second cleanup
    } catch (err) {
      error('Failed to setup MessageChannel communication:', err);
      throw err;
    }
  }

  /**
   * Handles messages from the target tab
   */
  function handleTargetTabMessage(event: any): void {
    const { id, method, params } = event.data;

    log(`Received message from target tab: ${method}`);

    // Execute the method and send response back
    executeMethodForTargetTab(id, method, params);
  }

  /**
   * Executes a method on behalf of the target tab and sends the response back
   */
  async function executeMethodForTargetTab(
    id: number,
    method: string,
    params: any[],
  ): Promise<void> {
    try {
      let result;

      switch (method) {
        case 'setupBrowserMetrics':
          result = await setupBrowserMetrics(params[0]);
          break;
        case 'dispatchMouseMove':
          result = await dispatchMouseMove(params[0], params[1]);
          break;
        case 'dispatchMouseDown':
          result = await dispatchMouseDown(params[0], params[1], params[2]);
          break;
        case 'dispatchMouseUp':
          result = await dispatchMouseUp(params[0], params[1], params[2]);
          break;
        case 'dispatchMouseClick':
          result = await dispatchMouseClick(params[0], params[1], params[2]);
          break;
        case 'dispatchKeyEvent':
          result = await dispatchKeyEvent(params[0], params[1]);
          break;
        case 'insertText':
          result = await insertText(params[0]);
          break;
        case 'takeScreenshot':
          result = await takeScreenshot();
          break;
        case 'handleInputEvent':
          result = await handleInputEvent(params[0]);
          break;
        case 'requestNewFrame':
          result = await requestNewFrame();
          break;
        case 'triggerMouseMovement':
          result = await triggerMouseMovement();
          break;
        default:
          throw new Error(`Unknown method: ${method}`);
      }

      // Send success response
      if (targetTabPort) {
        targetTabPort.postMessage({ id, response: result });
      }
    } catch (error) {
      // Send error response
      if (targetTabPort) {
        targetTabPort.postMessage({ id, error: (error as Error).message });
      }
    }
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   * Mimics puppeteer.connect() behavior by connecting to the existing page target
   * @param wsEndpoint - WebSocket endpoint
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string,
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log('Attaching to target:', targetId);

      const targetAttachedPromise = new Promise<string>((resolve) => {
        const handler = ({ params }: { params: any }) => {
          log('Browser controller attachedToTarget:', params);
          const { sessionId: newSessionId, targetInfo } = params;
          if (targetInfo.targetId === targetId) {
            sessionId = newSessionId;
            cdpClient?.Target.removeEventListener('attachedToTarget', handler);
            resolve(newSessionId);
          }
        };
        cdpClient?.Target.addEventListener('attachedToTarget', handler);
      });

      // Attach to the existing page target
      await cdpClient.Target.attachToTarget({
        targetId: targetId,
        flatten: true,
      });

      await targetAttachedPromise;

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      log('✓ Browser controller attached to target', targetId, 'with sessionId:', sessionId);
    } catch (err) {
      error('Failed to connect to CDP and attach to target:', err);
      throw err;
    }
  }

  /**
   * Sets up browser window bounds and device metrics
   * @param viewPort - Viewport dimensions
   */
  async function setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }

    try {
      const { windowId } = await cdpClient.Browser.getWindowForTarget({}, sessionId);
      await cdpClient.Browser.setWindowBounds(
        {
          windowId,
          bounds: {
            width: viewPort.width,
            height: viewPort.height + 75,
          },
        },
        sessionId,
      );

      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: viewPort.width,
          height: viewPort.height,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId,
      );

      log(`Browser metrics set to ${viewPort.width}x${viewPort.height}`);
    } catch (err) {
      error('Failed to setup browser metrics:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse movement event
   * @param x - X coordinate
   * @param y - Y coordinate
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseMoved',
          x: x,
          y: y,
          buttons: isMouseDown ? 1 : 0, // Reflect mouse button state
        },
        sessionId,
      );
      log(`Mouse moved to (${x}, ${y}), isMouseDown: ${isMouseDown}`);
    } catch (err) {
      error('Failed to dispatch mouse move:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse click event
   * @param x - X coordinate
   * @param y - Y coordinate
   * @param button - Mouse button ('left', 'right', etc.)
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );
      log(`Mouse clicked at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse click:', err);
      throw err;
    }
  }

  /**
   * Dispatches keyboard event
   * @param type - Event type ('keyDown', 'keyUp', 'char')
   * @param keyData - Key event data
   */
  async function dispatchKeyEvent(type: string, keyData: any): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }
    try {
      const eventData: any = {
        type: type,
        key: keyData.key,
      };

      if (type === 'char') {
        eventData.text = keyData.key;
      } else {
        eventData.code = keyData.code || keyData.key;
        eventData.keyCode = keyData.keyCode || keyData.key.charCodeAt(0);
      }

      // Enhanced special key handling with virtual key codes
      const specialKeyMap: { [key: string]: number } = {
        Backspace: 8,
        Tab: 9,
        Enter: 13,
        Escape: 27,
        Delete: 46,
        ArrowLeft: 37,
        ArrowUp: 38,
        ArrowRight: 39,
        ArrowDown: 40,
        Home: 36,
        End: 35,
        PageUp: 33,
        PageDown: 34,
        Insert: 45,
      };

      if (specialKeyMap[eventData.key]) {
        eventData.windowsVirtualKeyCode = specialKeyMap[eventData.key];
      }

      await cdpClient.Input.dispatchKeyEvent(eventData, sessionId);
    } catch (err) {
      error('Failed to dispatch key event:', err);
      throw err;
    }
  }

  /**
   * Inserts text directly using CDP's insertText method
   * This is more reliable than simulating individual key events
   * @param text - Text to insert
   */
  async function insertText(text: string): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }
    try {
      log('Inserting text directly:', text);
      await cdpClient.Input.insertText({ text: text }, sessionId);
    } catch (err) {
      error('Failed to insert text:', err);
      throw err;
    }
  }

  /**
   * Takes a screenshot using CDP
   * @returns Base64 encoded screenshot data or null on error
   */
  async function takeScreenshot(): Promise<string | null> {
    if (!cdpClient || !sessionId) {
      error('takeScreenshot called before browser controller initialization.');
      return null;
    }

    try {
      log('Capturing screenshot via CDP...');
      const screenshotResult = await cdpClient.Page.captureScreenshot(
        {
          format: 'png',
          captureBeyondViewport: false,
        },
        sessionId,
      );

      if (!screenshotResult?.data) {
        throw new Error('Page.captureScreenshot did not return data.');
      }

      log('Screenshot captured successfully.');
      return screenshotResult.data;
    } catch (err) {
      error('Error during takeScreenshot:', err);
      return null;
    }
  }

  /**
   * Triggers a mouse movement to generate new frames.
   * This is used when the UI doesn't settle within the expected time.
   */
  async function triggerMouseMovement(): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }

    try {
      // Generate random coordinates within a small area to avoid interfering with UI
      const x = Math.floor(Math.random() * 50) + 50; // Random x between 50-100
      const y = Math.floor(Math.random() * 50) + 50; // Random y between 50-100

      await dispatchMouseMove(x, y);
      log(`Triggered mouse movement to (${x}, ${y}) to generate frames`);
    } catch (err) {
      error('Failed to trigger mouse movement:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse down event
   * @param x - X coordinate
   * @param y - Y coordinate
   * @param button - Mouse button ('left', 'right', etc.)
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }
    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2, // 1 for left, 2 for right
        },
        sessionId,
      );
      isMouseDown = true;
      log(`Mouse down at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse down:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse up event
   * @param x - X coordinate
   * @param y - Y coordinate
   * @param button - Mouse button ('left', 'right', etc.)
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Browser controller not initialized');
    }
    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0, // No buttons pressed after release
        },
        sessionId,
      );
      isMouseDown = false;
      log(`Mouse up at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse up:', err);
      throw err;
    }
  }

  /**
   * Handles input events from WebRTC data channel
   * @param data - Input event data
   */
  async function handleInputEvent(data: any): Promise<void> {
    try {
      switch (data.type) {
        case 'mousedown':
          await dispatchMouseDown(data.x, data.y, data.button === 0 ? 'left' : 'right');
          break;
        case 'mouseup':
          await dispatchMouseUp(data.x, data.y, data.button === 0 ? 'left' : 'right');
          break;
        case 'mousemove':
          await dispatchMouseMove(data.x, data.y);
          break;
        case 'click':
          await dispatchMouseClick(data.x, data.y, data.button === 0 ? 'left' : 'right');
          break;

        // New character-based input handling
        case 'char-input':
          log(
            '✅ [CHAR-INPUT] Processing character:',
            data.text,
            'from:',
            data.source || 'unknown',
          );
          if (data.text !== undefined) {
            // Handle special characters
            if (data.text === '\b' || data.inputType === 'deleteContentBackward') {
              // Backspace
              await dispatchKeyEvent('keyDown', { key: 'Backspace' });
              await dispatchKeyEvent('keyUp', { key: 'Backspace' });
            } else if (data.text === '\x7F' || data.inputType === 'deleteContentForward') {
              // Delete
              await dispatchKeyEvent('keyDown', { key: 'Delete' });
              await dispatchKeyEvent('keyUp', { key: 'Delete' });
            } else if (
              data.text === '\n' ||
              data.inputType === 'insertLineBreak' ||
              data.inputType === 'insertParagraph'
            ) {
              // Enter/newline
              await dispatchKeyEvent('keyDown', { key: 'Enter' });
              await dispatchKeyEvent('keyUp', { key: 'Enter' });
            } else if (data.text && data.text.length > 0) {
              // Regular character input
              await insertText(data.text);
              log('✅ [CHAR-INPUT] Successfully inserted text:', data.text);
            } else {
              log('⚠️ [CHAR-INPUT] Empty character input, likely handled as special key');
            }
          } else {
            log('⚠️ [CHAR-INPUT] Warning: Undefined character input received');
          }
          break;

        case 'text-insert':
          log(
            '✅ [TEXT-INSERT] Processing text insertion:',
            data.text,
            'from:',
            data.source || 'unknown',
          );
          if (data.text && data.text.length > 0) {
            // Sanitize text to remove control characters
            const sanitizedText = data.text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
            if (sanitizedText.length > 0) {
              await insertText(sanitizedText);
              log('✅ [TEXT-INSERT] Successfully inserted sanitized text:', sanitizedText);
            } else {
              log('⚠️ [TEXT-INSERT] Warning: Text became empty after sanitization');
            }
          } else {
            log('⚠️ [TEXT-INSERT] Warning: Empty text insertion received');
          }
          break;

        case 'navigation-key':
          log('✅ [NAVIGATION-KEY] Processing navigation key:', data.key);
          // Handle pure navigation keys (arrows, function keys, etc.)
          await dispatchKeyEvent('keyDown', data);
          await dispatchKeyEvent('keyUp', data);
          break;

        // Legacy keyboard event handling (kept for compatibility)
        case 'keydown':
          log(
            '🔄 [LEGACY-KEYDOWN] Processing keydown:',
            data.key,
            'from:',
            data.source || 'unknown',
          );
          await dispatchKeyEvent('keyDown', data);
          break;
        case 'keyup':
          log('🔄 [LEGACY-KEYUP] Processing keyup:', data.key, 'from:', data.source || 'unknown');
          await dispatchKeyEvent('keyUp', data);
          break;
        case 'keypress':
          log(
            '🔄 [LEGACY-KEYPRESS] Processing keypress:',
            data.key,
            'from:',
            data.source || 'unknown',
          );
          await dispatchKeyEvent('char', data);
          break;
        default:
          log('Unhandled input type:', data.type);
      }
    } catch (err) {
      error('Failed to handle input event:', err);
    }
  }

  /**
   * Requests a new frame using invisible opacity changes that work on any page height.
   * Uses minimal CSS opacity changes to trigger browser repaints without being visible to users.
   */
  async function requestNewFrame(): Promise<void> {
    if (!cdpClient || !sessionId) {
      error('Browser controller not initialized for requestNewFrame');
      return;
    }

    try {
      log('Requesting new frame using invisible opacity technique');

      // Use invisible opacity changes to trigger browser repaints
      await cdpClient.Runtime.evaluate(
        {
          expression: `
            (async () => {
              try {
                const body = document.body || document.documentElement;
                const originalOpacity = body.style.opacity;

                // Trigger repaint with minimal opacity change (invisible to users)
                body.style.opacity = '0.9999';
                await new Promise(resolve => setTimeout(resolve, 16)); // One frame at 60fps
                body.style.opacity = '0.99999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = originalOpacity || '';

                console.log('[browserController] Opacity technique applied successfully');
                return {
                  success: true,
                  technique: 'opacity-change',
                  timestamp: Date.now(),
                  message: 'Invisible opacity frame generation completed'
                };
              } catch (e) {
                console.warn('[browserController] Opacity technique failed:', e);
                return {
                  success: false,
                  error: e.message,
                  timestamp: Date.now()
                };
              }
            })()
          `,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );

      log('Completed opacity frame generation technique');
    } catch (err) {
      error('Failed to request new frame via opacity technique:', err);
    }
  }

  // Expose public API
  (globalThis as any).browserController = {
    init,
    setupBrowserMetrics,
    takeScreenshot,
    handleInputEvent,
    requestNewFrame,
  };
})();
